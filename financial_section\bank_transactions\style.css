/* ===== تنسيق صفحة المعاملات البنكية ===== */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap');

:root {
    /* ألوان مشتركة */
    --light-color: #f5f7fa;
    --border-color: #e1e8ed;
    --text-dark: #2c3e50;
    --text-muted: #7f8c8d;
    --text-light: #ffffff;
    --danger-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --info-color: #3498db;
    
    /* تعريف متغيرات الألوان للتبديل بين المركزي والآجل */
    /* الخطأ الأولى هي قيم افتراضية وسيتم تغييرها ديناميكيًا */
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --accent-color: #1abc9c;
    --gradient-start: #3498db;
    --gradient-end: #2c3e50;
    --card-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
    --hover-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
    --card-bg: #ffffff;
    --table-header-bg: rgba(52, 152, 219, 0.08);
    --table-row-hover: rgba(52, 152, 219, 0.05);
    --positive-amount-color: #27ae60;
    --negative-amount-color: #e74c3c;
}

/* تطبيق ألوان البنوك المركزية */
.bank-type-markazi {
    --primary-color: #1e3a8a;
    --primary-dark: #172554;
    --secondary-color: #374151;
    --accent-color: #3b82f6;
    --gradient-start: #1e3a8a;
    --gradient-end: #172554;
    --table-header-bg: rgba(30, 58, 138, 0.08);
    --table-row-hover: rgba(30, 58, 138, 0.05);
}

/* تطبيق ألوان البنوك الآجلة */
.bank-type-ajel {
    --primary-color: #9f1239;
    --primary-dark: #7f1d1d;
    --secondary-color: #4b5563;
    --accent-color: #f43f5e;
    --gradient-start: #9f1239;
    --gradient-end: #7f1d1d;
    --table-header-bg: rgba(159, 18, 57, 0.08);
    --table-row-hover: rgba(159, 18, 57, 0.05);
}

/* إعدادات عامة */
body {
    font-family: 'Tajawal', sans-serif;
    background-color: var(--light-color);
    color: var(--text-dark);
    line-height: 1.6;
    margin: 0;
    padding: 0;
}

/* Dashboard Layout */
.dashboard-layout {
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    font-family: 'Tajawal', sans-serif;
    overflow-x: hidden;
}

/* Top Navigation Bar */
.top-navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 70px;
    background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-toggle-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.sidebar-toggle-btn:hover {
    background-color: rgba(255,255,255,0.1);
}

.navbar-brand {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.1rem;
    font-weight: 600;
}

.navbar-brand i {
    font-size: 1.3rem;
}

.navbar-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logout-btn {
    background-color: rgba(255, 255, 255, 0.15);
    color: var(--text-light);
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.logout-btn:hover {
    background-color: rgba(255, 255, 255, 0.25);
}

/* Main Content */
.main-content {
    margin-top: 70px;
    padding: 0;
    min-height: calc(100vh - 70px);
    transition: margin-right 0.3s ease;
}

.content-wrapper {
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
    display: flex;
    gap: 30px;
}

/* تنسيقات قسم البنوك الجانبي */
.banks-sidebar-section {
    position: sticky;
    top: 20px;
    height: fit-content;
    width: 300px;
    flex-shrink: 0;
}

/* تنسيقات المحتوى الرئيسي */
.main-dashboard-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.banks-sidebar-card {
    background-color: var(--card-bg);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 140px);
}

.banks-sidebar-card .sidebar-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
    background-color: #f8f9fa;
}

.banks-sidebar-card .sidebar-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.3rem;
}

.banks-sidebar-card .sidebar-list {
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-y: auto;
    flex: 1;
    max-height: 400px;
}

.banks-sidebar-card .sidebar-list li {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.banks-sidebar-card .sidebar-list li:last-child {
    border-bottom: none;
}

.banks-sidebar-card .sidebar-list a {
    display: flex;
    padding: 15px 20px;
    color: var(--text-dark);
    text-decoration: none;
    transition: all 0.3s ease;
}

.banks-sidebar-card .sidebar-list a:hover {
    background-color: var(--table-row-hover);
}

.banks-sidebar-card .sidebar-list a.active {
    background-color: var(--table-header-bg);
    color: var(--primary-color);
    font-weight: 600;
    border-right: 4px solid var(--primary-color);
}

/* تنسيق خاص لخيار "الكل" */
.banks-sidebar-card .sidebar-list a.all-banks-option {
    font-weight: bold;
    background-color: var(--secondary-color);
    color: var(--text-light);
    border: 2px solid var(--primary-color);
    margin-bottom: 10px;
    border-radius: 8px;
}

.banks-sidebar-card .sidebar-list a.all-banks-option:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--accent-color);
}

.banks-sidebar-card .sidebar-list a.all-banks-option.active {
    background-color: var(--accent-color);
    color: white;
    border-color: var(--accent-color);
    border-right: 4px solid var(--accent-color);
}

.banks-sidebar-card .sidebar-list a i {
    margin-left: 10px;
    color: var(--primary-color);
}

.banks-sidebar-card .sidebar-footer {
    padding: 15px;
    border-top: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.banks-sidebar-card .sidebar-btn {
    background-color: var(--secondary-color);
    color: var(--text-light);
    border: none;
    padding: 10px 15px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.3s ease;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.banks-sidebar-card .sidebar-btn:hover {
    background-color: var(--primary-dark);
}

/* Add Bank Button in Sidebar */
.banks-sidebar-card .sidebar-btn.add-btn {
    background-color: var(--success-color, #2ecc71);
    color: var(--text-light, #ffffff);
}

.banks-sidebar-card .sidebar-btn.add-btn:hover {
    background-color: #27ae60;
}

/* Switch Bank Type Button */
.banks-sidebar-card .sidebar-btn.switch-btn {
    background-color: var(--info-color, #3498db);
    color: var(--text-light, #ffffff);
    font-weight: 600;
}

.banks-sidebar-card .sidebar-btn.switch-btn:hover {
    background-color: #2980b9;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

/* Bank Type Specific Colors for Switch Button */
.bank-type-markazi .banks-sidebar-card .sidebar-btn.switch-btn {
    background-color: #e74c3c; /* أحمر للتبديل إلى الآجل */
}

.bank-type-markazi .banks-sidebar-card .sidebar-btn.switch-btn:hover {
    background-color: #c0392b;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
}

.bank-type-ajel .banks-sidebar-card .sidebar-btn.switch-btn {
    background-color: var(--primary-color, #3498db); /* أزرق للتبديل إلى المركزي */
}

.bank-type-ajel .banks-sidebar-card .sidebar-btn.switch-btn:hover {
    background-color: var(--primary-dark, #2980b9);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.banks-sidebar-card .loading-message {
    text-align: center;
    color: var(--text-muted);
    padding: 15px;
    font-style: italic;
}

/* تنسيقات المحتوى الرئيسي */
.dashboard-page-header {
    margin-bottom: 25px;
}

.dashboard-page-header h1 {
    color: var(--primary-color);
    margin: 0 0 8px 0;
    font-size: 1.8rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.dashboard-page-header p {
    color: var(--text-muted);
    margin: 0;
    font-size: 1.1rem;
}

.message {
    padding: 12px 15px;
    border-radius: 8px;
    margin: 15px 0;
    font-weight: 500;
    display: none;
}

.message.info {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(52, 152, 219, 0.2);
}

.message.success {
    background-color: rgba(46, 204, 113, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.2);
}

.message.error {
    background-color: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.2);
}

.message.warning {
    background-color: rgba(243, 156, 18, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(243, 156, 18, 0.2);
}

/* تنسيقات البطاقات */
.filter-card, .table-card {
    background-color: var(--card-bg);
    border-radius: 12px;
    margin-bottom: 25px;
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.card-body {
    padding: 20px;
}

/* تنسيقات التصفية */
.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    grid-gap: 15px;
}

.filter-actions {
    grid-column: 1 / -1;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 10px;
}

.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--secondary-color);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: 'Tajawal', sans-serif;
    color: var(--text-dark);
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);
}

.search-group {
    grid-column: span 2;
}

.filter-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.apply-filters-btn {
    background-color: var(--primary-color);
    color: white;
}

.apply-filters-btn:hover {
    background-color: var(--primary-dark);
}

.reset-filters-btn {
    background-color: #f0f0f0;
    color: var(--text-dark);
}

.reset-filters-btn:hover {
    background-color: #e0e0e0;
}

/* تنسيقات الجدول */
.table-responsive {
    overflow-x: auto;
}

#transactions-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 5px;
}

#transactions-table th,
#transactions-table td {
    padding: 12px 15px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

#transactions-table th {
    background-color: var(--table-header-bg);
    color: var(--primary-color);
    font-weight: 600;
}

#transactions-table tr:last-child td {
    border-bottom: none;
}

#transactions-table tr:hover {
    background-color: var(--table-row-hover);
}

.badge {
    background-color: var(--primary-color);
    color: white;
    padding: 4px 8px;
    border-radius: 30px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* تنسيقات مبالغ المعاملات */
.amount-positive {
    color: var(--positive-amount-color);
    font-weight: 600;
}

.amount-negative {
    color: var(--negative-amount-color);
    font-weight: 600;
}

/* تنسيقات أزرار الإجراءات في الجدول */
.action-btn {
    background-color: transparent;
    border: none;
    font-size: 1rem;
    padding: 5px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.edit-btn {
    color: var(--primary-color);
}

.edit-btn:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

.delete-btn {
    color: var(--danger-color);
}

.delete-btn:hover {
    background-color: rgba(231, 76, 60, 0.1);
}

.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* تنسيقات ترقيم الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 20px;
}

.pagination button {
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    border: none;
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination button:hover:not(:disabled) {
    background-color: var(--primary-color);
    color: white;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* تنسيقات أزرار الإضافة والتحكم */
.control-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.control-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.add-btn {
    background-color: var(--success-color);
}

/* تنسيقات النافذة المنبثقة */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background-color: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px;
    animation: modalFadeIn 0.3s ease-out;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content.large {
    max-width: 800px;
}

@keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

.close-modal-btn {
    position: absolute;
    left: 20px;
    top: 15px;
    font-size: 24px;
    color: var(--text-muted);
    background: none;
    border: none;
    cursor: pointer;
}

.close-modal-btn:hover {
    color: var(--danger-color);
}

.modal-content h2 {
    margin: 0;
    color: var(--primary-color);
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.modal .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 20px;
}

.modal .form-group-full-width {
    grid-column: 1 / -1;
}

.modal input,
.modal select,
.modal textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-family: 'Tajawal', sans-serif;
}

.modal input:focus,
.modal select:focus,
.modal textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.15);
}

.required {
    color: var(--danger-color);
}

.modal .form-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    padding: 0 20px 20px;
}

.submit-btn, .cancel-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    min-width: 120px;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.submit-btn {
    background-color: var(--primary-color);
    color: white;
}

.submit-btn:hover {
    background-color: var(--primary-dark);
}

.cancel-btn {
    background-color: #f0f0f0;
    color: var(--text-dark);
}

.cancel-btn:hover {
    background-color: #e0e0e0;
}

/* أنماط أيقونة المعلومات */
.tooltip-icon {
    cursor: help;
    color: var(--primary-color);
    margin-right: 5px;
}

/* تذييل الصفحة */
.main-footer {
    text-align: center;
    padding: 15px;
    background-color: var(--card-bg);
    border-top: 1px solid var(--border-color);
    color: var(--text-muted);
}

/* تحسينات للجوال والأجهزة اللوحية */
@media (max-width: 1200px) {
    .filter-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }

    .banks-sidebar-section {
        width: 280px;
    }

    .content-wrapper {
        gap: 20px;
    }
}

@media (max-width: 992px) {
    .content-wrapper {
        flex-direction: column;
        gap: 20px;
    }

    .banks-sidebar-section {
        position: static;
        width: 100%;
        order: -1;
    }

    .banks-sidebar-card {
        max-height: 300px;
    }
}

@media (max-width: 768px) {
    .navbar-brand span {
        display: none;
    }

    .content-wrapper {
        padding: 20px 15px;
        flex-direction: column;
    }

    .filter-grid {
        grid-template-columns: 1fr;
    }

    .search-group {
        grid-column: 1;
    }

    .modal-content {
        width: 95%;
    }

    .modal .form-grid {
        grid-template-columns: 1fr;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .card-header > div {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    #transactions-table {
        font-size: 0.9rem;
    }

    #transactions-table th,
    #transactions-table td {
        padding: 8px 10px;
    }
}

@media (max-width: 576px) {
    .filter-actions {
        flex-direction: column;
    }
    
    .filter-actions button {
        width: 100%;
    }
    
    .modal .form-actions {
        flex-direction: column;
    }
    
    .submit-btn, .cancel-btn {
        width: 100%;
    }
    
    .pagination button {
        min-width: 35px;
        height: 35px;
        padding: 0 8px;
        font-size: 0.9rem;
    }
}

/* تعريف متغيرات RGB للاستخدام في التعتيم والشفافية */
:root {
    --primary-color-rgb: 52, 152, 219; /* الافتراضي */
}

.bank-type-markazi {
    --primary-color-rgb: 30, 58, 138;
}

.bank-type-ajel {
    --primary-color-rgb: 159, 18, 57;
}

/* تنسيقات قسم الإحصائيات */
.statistics-section {
    margin-bottom: 25px;
}

.stats-cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stats-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius, 8px);
    box-shadow: var(--card-shadow);
    padding: 20px;
    display: flex;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    position: relative;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--hover-shadow);
}

.stats-icon {
    font-size: 2rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 20px;
    width: 50px;
}

.stats-content {
    flex: 1;
}

.stats-content h3 {
    font-size: 1rem;
    color: var(--secondary-color);
    margin: 0 0 8px 0;
    font-weight: 600;
}

.stats-content p {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 5px 0;
    white-space: nowrap;
}

.stats-content span {
    font-size: 0.85rem;
    color: var(--text-muted);
}

/* تنسيقات خاصة بكل بطاقة */
.stats-card.deposits .stats-icon {
    color: var(--success-color);
}

.stats-card.withdrawals .stats-icon {
    color: var(--danger-color);
}

.stats-card.net-balance .stats-icon {
    color: var(--warning-color);
}

.positive-amount {
    color: var(--success-color) !important;
}

.negative-amount {
    color: var(--danger-color) !important;
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .stats-cards-container {
        grid-template-columns: 1fr;
    }
    
    .stats-card {
        padding: 15px;
    }
    
    .stats-content p {
        font-size: 1.3rem;
    }
}
