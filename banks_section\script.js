console.log("Banks script loaded.");

// --- Auth Check ---
checkAuth('../auth.js'); // Adjusted path

// Supabase Initialization - using variables already defined in config.js
let supabaseUrl, supabaseAnonKey;

// Safely access the global variables
if (typeof SUPABASE_URL !== 'undefined') {
    supabaseUrl = SUPABASE_URL;
} else {
    supabaseUrl = 'YOUR_FALLBACK_SUPABASE_URL'; // Add fallback or handle error
    console.error('SUPABASE_URL not found in global scope.');
}

if (typeof SUPABASE_ANON_KEY !== 'undefined') {
    supabaseAnonKey = SUPABASE_ANON_KEY;
} else {
    supabaseAnonKey = 'YOUR_FALLBACK_SUPABASE_ANON_KEY'; // Add fallback or handle error
    console.error('SUPABASE_ANON_KEY not found in global scope.');
}

const { createClient } = supabase;
const _supabase = createClient(supabaseUrl, supabaseAnonKey);

console.log('Supabase Initialized for Banks:', _supabase);

// --- DOM Elements ---
const bankForm = document.getElementById('bank-form');
const formMessage = document.getElementById('form-message');
const addBankBtn = document.getElementById('add-bank-btn');
const addBankSection = document.getElementById('add-bank-section');
const cancelEditBtn = document.getElementById('cancel-edit-btn');
const closeFormBtn = document.getElementById('close-form-btn');
const banksTableBody = document.getElementById('banks-tbody');
const listMessage = document.getElementById('list-message');
const searchInput = document.getElementById('search-input');
const searchBtn = document.getElementById('search-btn');
const printReportBtn = document.getElementById('print-report-btn');
const bankIdField = document.getElementById('bank_id'); // Hidden input for bank ID (PK)
const formTitle = document.getElementById('form-title');
const totalBanksEl = document.getElementById('total-banks');
// Add elements for other stats if implemented
// const totalTransactionsEl = document.getElementById('total-transactions');
// const totalBalanceEl = document.getElementById('total-balance');
const banksCountBadge = document.getElementById('banks-count');
const paginationControls = document.getElementById('pagination-controls');
const backToFinanceBtnSidebar = document.getElementById('back-to-finance-btn-sidebar'); // Added
const dashboardMessage = document.getElementById('dashboard-message'); // Added for general messages

// Pay Due Modal Elements
const payDueBtn = document.getElementById('pay-due-btn');
const payDueModal = document.getElementById('pay-due-modal');
const closePayDueModal = document.getElementById('close-pay-due-modal');
const deferredBanksList = document.getElementById('deferred-banks-list');
const depositTransactionsList = document.getElementById('deposit-transactions-list');
const centralBankSelect = document.getElementById('central-bank-select');
const paymentForm = document.getElementById('payment-form');
const payDueMessage = document.getElementById('pay-due-message');

// Step navigation elements
const step1 = document.getElementById('step-1');
const step2 = document.getElementById('step-2');
const step3 = document.getElementById('step-3');
const backToStep1 = document.getElementById('back-to-step-1');
const backToStep2 = document.getElementById('back-to-step-2');
const selectedBankName = document.getElementById('selected-bank-name');
const dueAmount = document.getElementById('due-amount');
const deferredBankName = document.getElementById('deferred-bank-name');
const paymentDate = document.getElementById('payment-date');
const cancelPayment = document.getElementById('cancel-payment');

// Action buttons
const addBankBtn = document.getElementById('add-bank-btn');
const centralBanksBtn = document.getElementById('central-banks-btn');
const reviewTransactionsBtn = document.getElementById('review-transactions-btn');

// --- State ---
let currentBanks = [];
let editMode = false;
let stats = {
    totalBanks: 0,
    // Add other stats if needed
    // totalTransactions: 0,
    // totalBalance: 0
};

// Pagination variables
let currentPage = 1;
const banksPerPage = 12; // Number of banks per page
let totalPages = 1;

// Pay Due Modal State
let selectedDeferredBank = null;
let selectedTransaction = null;
let centralBanks = [];
let deferredBanks = [];
let currentStep = 1;

// --- Functions ---

// Function to display messages (form or list)
const showMessage = (element, message, type = 'info') => {
    if (!element) return; // Guard clause
    element.textContent = message;
    element.className = `message ${type} show`; // Ensure 'show' class is added

    // Auto hide success messages after 5 seconds
    if (type === 'success') {
        setTimeout(() => {
            element.style.display = 'none'; // Hide element directly
            element.classList.remove('show');
        }, 5000);
    } else {
         element.style.display = 'block'; // Ensure non-success messages are visible
    }
};

// Function to render pagination controls
const renderPaginationControls = () => {
    if (!paginationControls) return;
    paginationControls.innerHTML = '';

    if (!currentBanks || currentBanks.length <= banksPerPage) {
        return;
    }

    totalPages = Math.ceil(currentBanks.length / banksPerPage);

    // Previous button
    const prevButton = document.createElement('button');
    prevButton.innerHTML = '<i class="fas fa-angle-right"></i>';
    prevButton.className = currentPage === 1 ? 'disabled' : '';
    prevButton.disabled = currentPage === 1;
    prevButton.addEventListener('click', () => {
        if (currentPage > 1) {
            goToPage(currentPage - 1);
        }
    });
    paginationControls.appendChild(prevButton);

    // Page buttons logic (simplified for brevity)
    const maxPageButtons = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxPageButtons / 2));
    let endPage = Math.min(totalPages, startPage + maxPageButtons - 1);

    if (endPage - startPage + 1 < maxPageButtons) {
        startPage = Math.max(1, endPage - maxPageButtons + 1);
    }

    if (startPage > 1) {
        const firstPageBtn = document.createElement('button');
        firstPageBtn.textContent = '1';
        firstPageBtn.addEventListener('click', () => goToPage(1));
        paginationControls.appendChild(firstPageBtn);
        if (startPage > 2) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = document.createElement('button');
        pageBtn.textContent = i.toString();
        pageBtn.className = i === currentPage ? 'active' : '';
        pageBtn.addEventListener('click', () => goToPage(i));
        paginationControls.appendChild(pageBtn);
    }

     if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
             const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.className = 'pagination-ellipsis';
            paginationControls.appendChild(ellipsis);
        }
        const lastPageBtn = document.createElement('button');
        lastPageBtn.textContent = totalPages.toString();
        lastPageBtn.addEventListener('click', () => goToPage(totalPages));
        paginationControls.appendChild(lastPageBtn);
    }

    // Next button
    const nextButton = document.createElement('button');
    nextButton.innerHTML = '<i class="fas fa-angle-left"></i>';
    nextButton.className = currentPage === totalPages ? 'disabled' : '';
    nextButton.disabled = currentPage === totalPages;
    nextButton.addEventListener('click', () => {
        if (currentPage < totalPages) {
            goToPage(currentPage + 1);
        }
    });
    paginationControls.appendChild(nextButton);
};

// Function to navigate to a specific page
const goToPage = (page) => {
    currentPage = page;
    renderBanksTable(currentBanks); // Re-render table for the new page
};

// Function to get current page banks
const getCurrentPageBanks = () => {
    const startIndex = (currentPage - 1) * banksPerPage;
    const endIndex = startIndex + banksPerPage;
    return currentBanks.slice(startIndex, endIndex);
};

// Function to render the banks table
const renderBanksTable = (banks) => {
    if (!banksTableBody) return;
    banksTableBody.innerHTML = '';
    if (listMessage) listMessage.style.display = 'none';

    if (!banks || banks.length === 0) {
        // Adjusted colspan to 7
        banksTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">لا يوجد بنوك لعرضها.</td></tr>';
        if (banksCountBadge) banksCountBadge.textContent = '0';
        if (paginationControls) paginationControls.innerHTML = ''; // No pagination needed
        return;
    }

    // Update total count badge
    if (banksCountBadge) banksCountBadge.textContent = banks.length;

    // Get only banks for the current page
    const pageBanks = getCurrentPageBanks();

    pageBanks.forEach(bank => {
        const row = document.createElement('tr');
        // Updated row content to include IBAN, bank_type, contact_phone, notes and remove balances
        row.innerHTML = `
            <td>${bank.name || 'غير محدد'}</td>
            <td>${bank.account_number || '---'}</td>
            <td>${bank.iban || '---'}</td>
            <td>${bank.bank_type || '---'}</td> <!-- Added bank_type -->
            <td>${bank.contact_phone || '---'}</td>
            <td>${bank.notes || '---'}</td>
            <td>
                <button class="action-btn edit-btn" data-id="${bank.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete-btn" data-id="${bank.id}" title="حذف"><i class="fas fa-trash-alt"></i></button>
            </td>
        `;

        // Add event listeners for edit/delete buttons
        const editBtn = row.querySelector('.edit-btn');
        const deleteBtn = row.querySelector('.delete-btn');
        if (editBtn) {
            editBtn.addEventListener('click', () => handleEditBank(bank));
        }
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => handleDeleteBank(bank.id, bank.name));
        }

        banksTableBody.appendChild(row);
    });

    // Render pagination controls
    renderPaginationControls();
};

// Function to update dashboard stats
const updateDashboardStats = async () => {
    try {
        // Get total banks count (FILTERED FOR 'اجل')
        const { count: totalCount, error: totalError } = await _supabase
            .from('banks')
            .select('*', { count: 'exact', head: true })
            .eq('bank_type', 'آجل'); // Filter stats for 'آجل' banks

        // Add logic for other stats if needed

        if (!totalError) {
            stats.totalBanks = totalCount || 0;
            // Update other stats

            // Update dashboard elements
            if (totalBanksEl) totalBanksEl.textContent = stats.totalBanks;
            // Update other stat elements
        } else {
            console.error('Error fetching stats data:', totalError);
        }
    } catch (error) {
        console.error('Error updating stats:', error);
    }
};

// Function to fetch banks from Supabase
const fetchBanks = async (searchTerm = '') => {
    if (!banksTableBody) return;
    // Adjusted colspan to 7
    banksTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">جاري التحميل...</td></tr>';
    if (paginationControls) paginationControls.innerHTML = ''; // Clear pagination while loading

    try {
        let query = _supabase
            .from('banks') // Assuming table name is 'banks'
            .select('*')
            .eq('bank_type', 'آجل'); // Filter for 'آجل' banks

        // Add search condition if searchTerm is provided
        if (searchTerm) {
            // Search by name, account number, or IBAN within 'آجل' banks
            query = query.or(`name.ilike.%${searchTerm}%,account_number.ilike.%${searchTerm}%,iban.ilike.%${searchTerm}%`);
        }

        // Order by name
        query = query.order('name', { ascending: true });

        const { data, error } = await query;

        if (error) {
            console.error('Supabase Fetch Error:', error);
            showMessage(listMessage, `خطأ في جلب البيانات: ${error.message}`, 'error');
            // Adjusted colspan to 7
            banksTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">خطأ في تحميل البيانات.</td></tr>';
        } else {
            console.log('Fetched banks (آجل):', data);
            currentBanks = data || [];
            currentPage = 1; // Reset to first page
            renderBanksTable(currentBanks); // Render the table with fetched data

            if (data.length === 0 && searchTerm) {
                showMessage(listMessage, `لا توجد نتائج للبحث عن "${searchTerm}" ضمن البنوك الآجلة.`, 'info');
            } else if (data.length === 0) {
                 showMessage(listMessage, 'لا يوجد بنوك آجلة مسجلة حالياً.', 'info');
            }

            // Update dashboard stats after fetching (consider if stats should be filtered too)
            updateDashboardStats(); // This might need adjustment if stats should only reflect 'آجل' banks
        }
    } catch (error) {
        console.error('JavaScript Fetch Error:', error);
        showMessage(listMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
        // Adjusted colspan to 7
        banksTableBody.innerHTML = '<tr><td colspan="7" class="loading-message">خطأ غير متوقع.</td></tr>';
    }
};

// Function to handle form submission (Add or Update)
const handleFormSubmit = async (event) => {
    event.preventDefault();
    if (!bankForm || !formMessage) return;

    showMessage(formMessage, 'جاري حفظ البيانات...', 'info');
    const submitBtn = bankForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.disabled = true;

    const formData = new FormData(bankForm);
    const bankData = {};
    formData.forEach((value, key) => {
        // Trim whitespace, handle empty optional fields
        const trimmedValue = value.trim();
        // Handle optional fields: iban, contact_phone, notes, bank_type
        if (key === 'iban' || key === 'contact_phone' || key === 'notes' || key === 'bank_type') {
            bankData[key] = trimmedValue === '' ? null : trimmedValue;
        } else {
            bankData[key] = trimmedValue;
        }
    });

    const bankId = bankData.bank_id; // Get the hidden ID (PK)

    // Basic validation (Name and Account Number required based on UI)
    if (!bankData.bank_name || !bankData.account_number) {
        showMessage(formMessage, 'الرجاء ملء اسم البنك ورقم الحساب (*).', 'error');
        if (submitBtn) submitBtn.disabled = false;
        return;
    }
    // Optional: Validate bank_type if it becomes required
    // if (!bankData.bank_type) {
    //     showMessage(formMessage, 'الرجاء اختيار نوع البنك.', 'error');
    //     if (submitBtn) submitBtn.disabled = false;
    //     return;
    // }


    // Prepare data for Supabase (match column names)
    const dataToUpsert = {
        name: bankData.bank_name,
        account_number: bankData.account_number,
        iban: bankData.iban, // Added IBAN
        bank_type: bankData.bank_type, // Added bank_type
        contact_phone: bankData.contact_phone, // Added contact_phone
        notes: bankData.notes, // Added notes
    };

    try {
        let result;
        if (editMode && bankId) {
            // Update existing bank
            result = await _supabase
                .from('banks')
                .update(dataToUpsert)
                .eq('id', bankId) // Use 'id' (uuid) as the primary key
                .select();
        } else {
            // Insert new bank
            result = await _supabase
                .from('banks')
                .insert([dataToUpsert])
                .select();
        }

        const { data, error } = result;

        if (error) {
            console.error('Supabase Save Error:', error);
            let userMessage = `خطأ في الحفظ: ${error.message}`;
            if (error.code === '23505') { // Unique constraint violation
                 // Assuming unique constraint might be on name or account_number
                 userMessage = 'خطأ: اسم البنك أو رقم الحساب موجود مسبقاً.';
            } else if (error.message.includes('invalid input value for enum bank_type_enum')) {
                 userMessage = 'خطأ: قيمة نوع البنك غير صالحة.';
            }
            showMessage(formMessage, userMessage, 'error');
        } else {
            console.log('Supabase Save Success:', data);
            showMessage(formMessage, `تم ${editMode ? 'تحديث' : 'إضافة'} البنك بنجاح!`, 'success');
            // Keep modal open for inline form
            // setTimeout(() => {
            //     toggleModal(false); // Don't close inline form automatically
            // }, 1500);
            resetForm(); // Reset the form after successful save
            fetchBanks(); // Refresh list
        }
    } catch (error) {
        console.error('JavaScript Save Error:', error);
        showMessage(formMessage, `حدث خطأ غير متوقع: ${error.message}`, 'error');
    } finally {
        if (submitBtn) submitBtn.disabled = false;
    }
};

// Function to reset the form and UI elements
const resetForm = () => {
    editMode = false;
    if (bankForm) bankForm.reset();
    if (bankIdField) bankIdField.value = ''; // Clear hidden ID field
    if (formMessage) formMessage.style.display = 'none';
    if (formTitle) formTitle.textContent = 'إضافة بنك جديد';
    const submitBtn = bankForm?.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'حفظ البنك'; // Reset button text
    const cancelBtn = bankForm?.querySelector('#cancel-edit-btn');
    if (cancelBtn) cancelBtn.style.display = 'none'; // Hide cancel button
};

// Function to populate form for editing
const handleEditBank = (bank) => {
    if (!bank || !bankForm) return;
    // No need to toggle modal for inline form, just populate
    editMode = true;

    if (formTitle) formTitle.textContent = 'تعديل بيانات البنك';
    const submitBtn = bankForm.querySelector('.submit-btn');
    if (submitBtn) submitBtn.textContent = 'تحديث البنك';
    const cancelBtn = bankForm.querySelector('#cancel-edit-btn');
    if (cancelBtn) cancelBtn.style.display = 'inline-block'; // Show cancel button

    // Populate form fields
    if (bankIdField) bankIdField.value = bank.id || ''; // Set the hidden ID field
    document.getElementById('bank_name').value = bank.name || '';
    document.getElementById('account_number').value = bank.account_number || '';
    document.getElementById('iban').value = bank.iban || ''; // Populate IBAN
    document.getElementById('bank_type').value = bank.bank_type || ''; // Populate bank_type
    document.getElementById('contact_phone').value = bank.contact_phone || ''; // Populate contact phone
    document.getElementById('notes').value = bank.notes || ''; // Populate notes
    // Removed initial_balance population

    // Scroll to form and focus
    bankForm.scrollIntoView({ behavior: 'smooth', block: 'start' });
    setTimeout(() => {
        document.getElementById('bank_name')?.focus();
    }, 300); // Delay focus slightly
};

// Function to handle bank deletion
const handleDeleteBank = async (bankId, bankName) => {
    const confirmDelete = confirm(`هل أنت متأكد من حذف بنك "${bankName || 'هذا البنك'}"؟ لا يمكن التراجع عن هذا الإجراء.`);

    if (confirmDelete) {
        try {
            const { error } = await _supabase
                .from('banks')
                .delete()
                .eq('id', bankId); // Use 'id' (uuid) as the PK

            if (error) {
                console.error('Supabase Delete Error:', error);
                 // Check for foreign key constraint violation (e.g., if transactions are linked)
                let userMessage = `خطأ في الحذف: ${error.message}`;
                if (error.code === '23503') {
                    userMessage = 'خطأ: لا يمكن حذف البنك لوجود معاملات أو بيانات أخرى مرتبطة به.';
                }
                showMessage(listMessage, userMessage, 'error');
            } else {
                console.log('Bank deleted:', bankId);
                showMessage(listMessage, `تم حذف البنك بنجاح.`, 'success');
                fetchBanks(); // Refresh the list
            }
        } catch (error) {
            console.error('JavaScript Delete Error:', error);
            showMessage(listMessage, `حدث خطأ غير متوقع أثناء الحذف: ${error.message}`, 'error');
        }
    }
};

// Function to handle search
const handleSearch = () => {
    if (!searchInput) return;
    const searchTerm = searchInput.value.trim();
    fetchBanks(searchTerm);
};

// === Pay Due Modal Functions ===

// Function to fetch central banks
const fetchCentralBanks = async () => {
    try {
        const { data, error } = await _supabase
            .from('banks')
            .select('id, name')
            .eq('bank_type', 'مركزي')
            .order('name');

        if (error) throw error;
        centralBanks = data || [];
        populateCentralBankSelect();
        return centralBanks;
    } catch (error) {
        console.error('Error fetching central banks:', error);
        showMessage(payDueMessage, `خطأ في جلب البنوك المركزية: ${error.message}`, 'error');
        return [];
    }
};

// Function to fetch deferred banks
const fetchDeferredBanks = async () => {
    try {
        const { data, error } = await _supabase
            .from('banks')
            .select('id, name, account_number')
            .eq('bank_type', 'آجل')
            .order('name');

        if (error) throw error;
        deferredBanks = data || [];
        return deferredBanks;
    } catch (error) {
        console.error('Error fetching deferred banks:', error);
        showMessage(payDueMessage, `خطأ في جلب البنوك الآجلة: ${error.message}`, 'error');
        return [];
    }
};

// Function to populate central bank select
const populateCentralBankSelect = () => {
    if (!centralBankSelect) return;

    centralBankSelect.innerHTML = '<option value="">اختر البنك المركزي</option>';
    centralBanks.forEach(bank => {
        const option = document.createElement('option');
        option.value = bank.id;
        option.textContent = bank.name;
        centralBankSelect.appendChild(option);
    });
};

// Function to show pay due modal
const showPayDueModal = async () => {
    if (!payDueModal) return;

    // Reset modal state
    currentStep = 1;
    selectedDeferredBank = null;
    selectedTransaction = null;

    // Show loading
    showMessage(payDueMessage, 'جاري تحميل البيانات...', 'info');

    try {
        // Fetch required data
        await Promise.all([
            fetchCentralBanks(),
            fetchDeferredBanks()
        ]);

        // Populate deferred banks list
        await populateDeferredBanksList();

        // Show modal
        payDueModal.classList.add('show');
        showStep(1);

        // Set default date to today
        if (paymentDate) {
            paymentDate.value = new Date().toISOString().split('T')[0];
        }

        showMessage(payDueMessage, '', 'info');
    } catch (error) {
        console.error('Error showing pay due modal:', error);
        showMessage(payDueMessage, `خطأ في تحميل البيانات: ${error.message}`, 'error');
    }
};

// Function to hide pay due modal
const hidePayDueModal = () => {
    if (!payDueModal) return;
    payDueModal.classList.remove('show');
    resetPayDueModal();
};

// Function to reset pay due modal
const resetPayDueModal = () => {
    currentStep = 1;
    selectedDeferredBank = null;
    selectedTransaction = null;

    if (paymentForm) paymentForm.reset();
    if (payDueMessage) payDueMessage.style.display = 'none';

    // Set default date
    if (paymentDate) {
        paymentDate.value = new Date().toISOString().split('T')[0];
    }
};

// Function to show specific step
const showStep = (stepNumber) => {
    // Hide all steps
    [step1, step2, step3].forEach(step => {
        if (step) step.classList.remove('active');
    });

    // Show selected step
    const targetStep = document.getElementById(`step-${stepNumber}`);
    if (targetStep) {
        targetStep.classList.add('active');
        currentStep = stepNumber;
    }
};

// Function to populate deferred banks list
const populateDeferredBanksList = async () => {
    if (!deferredBanksList) return;

    deferredBanksList.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

    try {
        const banks = await fetchDeferredBanks();

        if (banks.length === 0) {
            deferredBanksList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-university"></i>
                    <h5>لا توجد بنوك آجلة</h5>
                    <p>لا توجد بنوك آجلة مسجلة في النظام</p>
                </div>
            `;
            return;
        }

        deferredBanksList.innerHTML = '';
        banks.forEach(bank => {
            const bankItem = document.createElement('div');
            bankItem.className = 'bank-item';
            bankItem.innerHTML = `
                <h5>${bank.name}</h5>
                <p>رقم الحساب: ${bank.account_number || 'غير محدد'}</p>
            `;

            bankItem.addEventListener('click', () => selectDeferredBank(bank));
            deferredBanksList.appendChild(bankItem);
        });

    } catch (error) {
        console.error('Error populating deferred banks list:', error);
        deferredBanksList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h5>خطأ في التحميل</h5>
                <p>حدث خطأ أثناء تحميل البنوك الآجلة</p>
            </div>
        `;
    }
};

// Function to select deferred bank
const selectDeferredBank = async (bank) => {
    selectedDeferredBank = bank;

    // Update UI
    document.querySelectorAll('.bank-item').forEach(item => {
        item.classList.remove('selected');
    });
    event.target.closest('.bank-item').classList.add('selected');

    // Load deposit transactions for this bank
    await loadDepositTransactions(bank.id);

    // Update step 2 header
    if (selectedBankName) {
        selectedBankName.textContent = `معاملات الإيداع - ${bank.name}`;
    }

    // Move to step 2
    showStep(2);
};

// Function to load deposit transactions
const loadDepositTransactions = async (bankId) => {
    if (!depositTransactionsList) return;

    depositTransactionsList.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> جاري التحميل...</div>';

    try {
        const { data, error } = await _supabase
            .from('bank_transactions')
            .select('*')
            .eq('bank_id', bankId)
            .eq('transaction_type', 'deposit')
            .order('transaction_date', { ascending: false });

        if (error) throw error;

        if (!data || data.length === 0) {
            depositTransactionsList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-receipt"></i>
                    <h5>لا توجد معاملات إيداع</h5>
                    <p>لا توجد معاملات إيداع لهذا البنك</p>
                </div>
            `;
            return;
        }

        depositTransactionsList.innerHTML = '';
        data.forEach(transaction => {
            const transactionItem = document.createElement('div');
            transactionItem.className = 'transaction-item';
            transactionItem.innerHTML = `
                <div class="transaction-info">
                    <h6>${transaction.description || 'معاملة إيداع'}</h6>
                    <p>التاريخ: ${new Date(transaction.transaction_date).toLocaleDateString('ar-SA')}</p>
                    ${transaction.reference_code ? `<p>المرجع: ${transaction.reference_code}</p>` : ''}
                </div>
                <div class="transaction-amount">${transaction.amount.toLocaleString()} ر.س</div>
                <button class="pay-transaction-btn" onclick="selectTransaction('${transaction.id}', ${transaction.amount})">
                    <i class="fas fa-credit-card"></i> دفع
                </button>
            `;

            depositTransactionsList.appendChild(transactionItem);
        });

    } catch (error) {
        console.error('Error loading deposit transactions:', error);
        depositTransactionsList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h5>خطأ في التحميل</h5>
                <p>حدث خطأ أثناء تحميل المعاملات</p>
            </div>
        `;
    }
};

// Function to select transaction for payment
const selectTransaction = (transactionId, amount) => {
    selectedTransaction = { id: transactionId, amount: amount };

    // Update payment summary
    if (dueAmount) dueAmount.textContent = `${amount.toLocaleString()} ر.س`;
    if (deferredBankName) deferredBankName.textContent = selectedDeferredBank?.name || '-';

    // Move to step 3
    showStep(3);
};

// Function to handle payment form submission
const handlePaymentSubmit = async (event) => {
    event.preventDefault();

    if (!selectedDeferredBank || !selectedTransaction) {
        showMessage(payDueMessage, 'خطأ: لم يتم اختيار البنك أو المعاملة', 'error');
        return;
    }

    const formData = new FormData(paymentForm);
    const centralBankId = formData.get('central-bank-select') || centralBankSelect.value;
    const paymentDateValue = formData.get('payment-date') || paymentDate.value;
    const paymentDetails = formData.get('payment-details') || document.getElementById('payment-details').value;

    if (!centralBankId || !paymentDateValue) {
        showMessage(payDueMessage, 'الرجاء ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // Disable submit button
    const submitBtn = paymentForm.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري المعالجة...';
    }

    showMessage(payDueMessage, 'جاري معالجة الدفع...', 'info');

    try {
        // Create payment record in financial_transactions_log
        const { data: logData, error: logError } = await _supabase
            .from('financial_transactions_log')
            .insert([{
                transaction_date: paymentDateValue,
                amount: selectedTransaction.amount,
                transaction_type: 'transfer',
                description: `دفع مستحق من البنك الآجل ${selectedDeferredBank.name} إلى البنك المركزي${paymentDetails ? ' - ' + paymentDetails : ''}`,
                source_table: 'deferred_bank_payment',
                source_record_id: selectedTransaction.id,
                bank_id: centralBankId,
                budget_month_id: null,
                is_reversal: false
            }])
            .select()
            .single();

        if (logError) throw logError;

        showMessage(payDueMessage, 'تم تسجيل الدفع بنجاح! جاري إنشاء المعاملة العكسية...', 'success');

        // Wait a moment for triggers to process
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Create reverse transaction for the deferred bank
        const { data: reverseData, error: reverseError } = await _supabase
            .from('bank_transactions')
            .insert([{
                bank_id: selectedDeferredBank.id,
                amount: selectedTransaction.amount,
                transaction_type: 'withdrawal',
                transaction_date: paymentDateValue,
                description: `دفع مستحق - معاملة عكسية للإيداع رقم ${selectedTransaction.id}`,
                transaction_source: 'deferred_payment_reversal',
                reference_id: selectedTransaction.id,
                reference_code: `REV-${selectedTransaction.id}`,
                reference_table: 'bank_transactions'
            }])
            .select()
            .single();

        if (reverseError) throw reverseError;

        // Check if amounts cancel out and delete if they equal zero
        await checkAndDeleteCancelingTransactions(selectedDeferredBank.id, selectedTransaction.id);

        showMessage(payDueMessage, 'تم إكمال عملية الدفع بنجاح!', 'success');

        // Close modal after success
        setTimeout(() => {
            hidePayDueModal();
        }, 2000);

    } catch (error) {
        console.error('Error processing payment:', error);
        showMessage(payDueMessage, `خطأ في معالجة الدفع: ${error.message}`, 'error');
    } finally {
        // Re-enable submit button
        if (submitBtn) {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-check"></i> تأكيد الدفع';
        }
    }
};

// Function to check and delete canceling transactions
const checkAndDeleteCancelingTransactions = async (bankId, originalTransactionId) => {
    try {
        // Get the original deposit transaction
        const { data: originalTransaction, error: originalError } = await _supabase
            .from('bank_transactions')
            .select('*')
            .eq('id', originalTransactionId)
            .single();

        if (originalError) throw originalError;

        // Get the reverse transaction we just created
        const { data: reverseTransactions, error: reverseError } = await _supabase
            .from('bank_transactions')
            .select('*')
            .eq('bank_id', bankId)
            .eq('reference_id', originalTransactionId)
            .eq('transaction_type', 'withdrawal');

        if (reverseError) throw reverseError;

        if (reverseTransactions && reverseTransactions.length > 0) {
            const reverseTransaction = reverseTransactions[0];

            // Check if amounts are equal (canceling out)
            if (Math.abs(originalTransaction.amount - reverseTransaction.amount) < 0.01) {
                // Delete both transactions as they cancel each other out
                const { error: deleteOriginalError } = await _supabase
                    .from('bank_transactions')
                    .delete()
                    .eq('id', originalTransactionId);

                if (deleteOriginalError) throw deleteOriginalError;

                const { error: deleteReverseError } = await _supabase
                    .from('bank_transactions')
                    .delete()
                    .eq('id', reverseTransaction.id);

                if (deleteReverseError) throw deleteReverseError;

                console.log('Deleted canceling transactions:', originalTransactionId, reverseTransaction.id);
            }
        }

    } catch (error) {
        console.error('Error checking/deleting canceling transactions:', error);
        // Don't throw error here as the main payment was successful
    }
};

// Function to handle printing report
const handlePrintReport = () => {
    const banksToPrint = [...currentBanks];
    let reportTitle = 'تقرير البنوك';

    // Sort by bank name
    banksToPrint.sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    const printWindow = window.open('', '_blank', 'height=600,width=800');
    let reportContent = `
        <html dir="rtl">
        <head>
            <title>${reportTitle}</title>
            <style>
                body { font-family: 'Tajawal', sans-serif; padding: 20px; direction: rtl; }
                h1 { text-align: center; margin-bottom: 10px; color: #333; }
                .print-meta { display: flex; justify-content: space-between; margin-bottom: 20px; font-size: 0.9em; color: #555; }
                table { width: 100%; border-collapse: collapse; margin-top: 15px; }
                th, td { padding: 8px; text-align: right; border: 1px solid #ddd; font-size: 0.9em; word-wrap: break-word; } /* Added word-wrap */
                th { background-color: #f2f2f2; font-weight: bold; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                @media print {
                    .no-print { display: none; }
                    body { padding: 5px; }
                    h1 { font-size: 1.5em; }
                }
            </style>
        </head>
        <body>
            <div class="print-meta">
                <span>تاريخ التقرير: ${new Date().toLocaleDateString('ar-SA')}</span>
                <span>عدد البنوك في التقرير: ${banksToPrint.length}</span>
            </div>
            <h1>${reportTitle}</h1>
            ${banksToPrint.length === 0 ? '<p style="text-align: center; color: #888;">لا يوجد بنوك لعرضها.</p>' : `
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم البنك</th>
                        <th>رقم الحساب</th>
                        <th>الآيبان</th> <!-- Added IBAN -->
                        <th>نوع البنك</th> <!-- Added Bank Type -->
                        <th>هاتف الاتصال</th> <!-- Added Contact Phone -->
                        <th>ملاحظات</th> <!-- Added Notes -->
                        <!-- Removed Balance Headers -->
                    </tr>
                </thead>
                <tbody>
            `}
    `;

    banksToPrint.forEach((bank, index) => {
        // Updated row content for print
        reportContent += `
            <tr>
                <td>${index + 1}</td>
                <td>${bank.name || ''}</td>
                <td>${bank.account_number || ''}</td>
                <td>${bank.iban || ''}</td>
                <td>${bank.bank_type || ''}</td> <!-- Added bank_type -->
                <td>${bank.contact_phone || ''}</td>
                <td>${bank.notes || ''}</td>
            </tr>
        `;
    });

    if (banksToPrint.length > 0) {
        reportContent += `
                </tbody>
            </table>
        `;
    }

    reportContent += `
            <div class="no-print" style="margin-top: 20px; text-align: center;">
                <button onclick="window.print()">طباعة</button>
                <button onclick="window.close()">إغلاق</button>
            </div>
        </body>
        </html>
    `;

    printWindow.document.write(reportContent);
    printWindow.document.close();
};


// Function to handle modal toggling (Now less relevant for inline form, but kept for potential future use)
function toggleModal(show = true) {
    // This function might not be needed if the form is always inline
    // Kept for potential future refactoring or if a modal is reintroduced
    console.warn('toggleModal called, but form is likely inline.');
    // If you revert to a modal, uncomment the logic below
    /*
    if (!addBankSection) return;
    console.log('Bank Modal toggled:', show ? 'show' : 'hide');

    if (show) {
        resetForm(); // Reset form before showing
        addBankSection.classList.add('show');
        setTimeout(() => {
            document.getElementById('bank_name')?.focus();
        }, 300);
        document.body.style.overflow = 'hidden';
    } else {
        addBankSection.classList.remove('show');
        document.body.style.overflow = '';
        resetForm(); // Also reset when closing modal
    }
    */
}

// --- Initial Load ---
document.addEventListener('DOMContentLoaded', async () => {
    console.log('DOM loaded, initializing banks application...');

    // Setup event listeners
    setupEventListeners();

    // Fetch initial banks data
    fetchBanks();
});


// --- Event Listeners ---
const setupEventListeners = () => {
    if (bankForm) {
        bankForm.addEventListener('submit', handleFormSubmit);
    }

    // Remove listener for addBankBtn if form is inline
    // if (addBankBtn) {
    //     addBankBtn.addEventListener('click', () => {
    //         editMode = false;
    //         // toggleModal(true); // Don't toggle modal for inline
    //         resetForm(); // Just reset the inline form
    //         document.getElementById('bank_name')?.focus();
    //     });
    // }

    if (cancelEditBtn) {
        cancelEditBtn.addEventListener('click', () => {
            // toggleModal(false); // Don't toggle modal for inline
            resetForm(); // Reset the form when cancelling edit
        });
    }

    // Remove listener for closeFormBtn if form is inline
    // if (closeFormBtn) {
    //     closeFormBtn.addEventListener('click', () => {
    //         toggleModal(false);
    //     });
    // }

    // Remove modal background click listener if form is inline
    // if (addBankSection) {
    //     addBankSection.addEventListener('click', (event) => {
    //         if (event.target === addBankSection) {
    //             toggleModal(false);
    //         }
    //     });
    // }

    if (searchBtn) {
        searchBtn.addEventListener('click', handleSearch);
    }

    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
        // Optional: Add input event listener for live search
        // searchInput.addEventListener('input', handleSearch);
    }

    if (printReportBtn) {
        printReportBtn.addEventListener('click', handlePrintReport);
    }

    // Remove Escape key listener if form is inline (or adjust if needed)
    // document.addEventListener('keydown', function(event) {
    //     if (event.key === 'Escape' && addBankSection && addBankSection.classList.contains('show')) {
    //         toggleModal(false);
    //     }
    // });

    // Back button listener (sidebar)
    if (backToFinanceBtnSidebar) {
        backToFinanceBtnSidebar.addEventListener('click', () => {
            // Navigate to the main financial dashboard
            window.location.href = '../financial_section/financial_dashboard.html'; // Adjust path if needed
        });
    } else {
         console.error("Back to Finance Sidebar Button not found!");
    }

    // === Pay Due Modal Event Listeners ===

    // Pay Due button
    if (payDueBtn) {
        payDueBtn.addEventListener('click', showPayDueModal);
    }

    // Close modal button
    if (closePayDueModal) {
        closePayDueModal.addEventListener('click', hidePayDueModal);
    }

    // Modal background click to close
    if (payDueModal) {
        payDueModal.addEventListener('click', (event) => {
            if (event.target === payDueModal) {
                hidePayDueModal();
            }
        });
    }

    // Step navigation buttons
    if (backToStep1) {
        backToStep1.addEventListener('click', () => showStep(1));
    }

    if (backToStep2) {
        backToStep2.addEventListener('click', () => showStep(2));
    }

    // Payment form submission
    if (paymentForm) {
        paymentForm.addEventListener('submit', handlePaymentSubmit);
    }

    // Cancel payment button
    if (cancelPayment) {
        cancelPayment.addEventListener('click', hidePayDueModal);
    }

    // === Action Buttons Event Listeners ===

    // Add Bank button
    if (addBankBtn) {
        addBankBtn.addEventListener('click', () => {
            // Scroll to form
            if (bankForm) {
                bankForm.scrollIntoView({ behavior: 'smooth', block: 'start' });
                setTimeout(() => {
                    document.getElementById('bank_name')?.focus();
                }, 300);
            }
        });
    }

    // Central Banks button
    if (centralBanksBtn) {
        centralBanksBtn.addEventListener('click', () => {
            // Navigate to central banks page
            window.location.href = '../financial_section/bank_transactions/bank_transactions.html?type=مركزي';
        });
    }

    // Review Transactions button
    if (reviewTransactionsBtn) {
        reviewTransactionsBtn.addEventListener('click', () => {
            // Navigate to deferred bank transactions page
            window.location.href = '../financial_section/bank_transactions/bank_transactions.html?type=آجل';
        });
    }

    // Escape key to close modal
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape' && payDueModal && payDueModal.classList.contains('show')) {
            hidePayDueModal();
        }
    });
};

// Make selectTransaction available globally for onclick handlers
window.selectTransaction = selectTransaction;
