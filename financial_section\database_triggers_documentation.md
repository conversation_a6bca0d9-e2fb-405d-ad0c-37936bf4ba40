# شرح نظام التريجرات المالية في قاعدة البيانات

## 📋 نظرة عامة

يحتوي النظام على ثلاثة جداول رئيسية مترابطة عبر نظام تريجرات متقدم:

1. **`student_payments`** - جدول دفعات الطلاب
2. **`financial_transactions_log`** - سجل المعاملات المالية 
3. **`bank_transactions`** - المعاملات البنكية

## 🏗️ هيكل الجداول

### جدول `student_payments`
```sql
- id (UUID) - معرف الدفعة
- student_id (UUID) - معرف الطالب
- budget_month_id (UUID) - معرف الشهر المالي
- bank_id (UUID) - معرف البنك
- amount (NUMERIC) - مبلغ الدفعة
- payment_date (DATE) - تاريخ الدفعة
- payment_status (TEXT) - حالة الدفعة
- created_at (TIMESTAMP) - تاريخ الإنشاء
```

### جدول `financial_transactions_log`
```sql
- id (UUID) - معرف السجل المالي
- transaction_date (DATE) - تاريخ المعاملة
- amount (NUMERIC) - المبلغ
- transaction_type (ENUM) - نوع المعاملة (إيداع/سحب)
- description (TEXT) - الوصف
- source_table (VARCHAR) - الجدول المصدر
- source_record_id (TEXT) - معرف السجل المصدر
- bank_id (UUID) - معرف البنك
- budget_month_id (UUID) - معرف الشهر المالي
- is_reversal (BOOLEAN) - هل هو سجل عكسي
- original_log_entry_id (UUID) - معرف السجل الأصلي (للسجلات العكسية)
- created_at (TIMESTAMP) - تاريخ الإنشاء
- updated_at (TIMESTAMP) - تاريخ التحديث
```

### جدول `bank_transactions`
```sql
- id (UUID) - معرف المعاملة البنكية
- bank_id (UUID) - معرف البنك
- amount (NUMERIC) - المبلغ
- transaction_type (ENUM) - نوع المعاملة
- transaction_date (DATE) - تاريخ المعاملة
- description (TEXT) - الوصف
- transaction_source (TEXT) - مصدر المعاملة
- reference_id (UUID) - معرف المرجع
- reference_table (TEXT) - جدول المرجع
- budget_month_id (UUID) - معرف الشهر المالي
- financial_transaction_log_id (UUID) - معرف السجل المالي المرتبط
- created_at (TIMESTAMP) - تاريخ الإنشاء
- updated_at (TIMESTAMP) - تاريخ التحديث
```

## 🔄 سير العمل

### 1. إضافة دفعة جديدة

```mermaid
graph TD
    A[إدراج دفعة في student_payments] --> B[تريجر handle_student_payment_changes_v2]
    B --> C[إنشاء سجل في financial_transactions_log]
    C --> D[تريجر sync_log_to_bank_transactions]
    D --> E[إنشاء معاملة في bank_transactions]
```

**الخطوات:**
1. يتم إدراج دفعة جديدة في `student_payments`
2. يتم تشغيل تريجر `handle_student_payment_changes_v2`
3. ينشئ التريجر سجل جديد في `financial_transactions_log` بنوع "إيداع"
4. يتم تشغيل تريجر `sync_log_to_bank_transactions`
5. ينشئ التريجر معاملة بنكية جديدة في `bank_transactions` مرتبطة بالسجل المالي

### 2. تعديل دفعة موجودة

```mermaid
graph TD
    A[تعديل دفعة في student_payments] --> B[تريجر handle_student_payment_changes_v2]
    B --> C[إنشاء سجل عكسي للقيمة القديمة]
    C --> D[تريجر sync_log_to_bank_transactions للسجل العكسي]
    D --> E[إلغاء ربط المعاملة البنكية]
    E --> F[إنشاء سجل جديد للقيمة الجديدة]
    F --> G[تريجر sync_log_to_bank_transactions للسجل الجديد]
    G --> H[ربط المعاملة البنكية بالسجل الجديد]
```

**الخطوات:**
1. يتم تعديل دفعة في `student_payments`
2. يتم تشغيل تريجر `handle_student_payment_changes_v2`
3. ينشئ التريجر سجل عكسي (سحب) للقيمة القديمة مع `is_reversal = true`
4. يتم تشغيل تريجر `sync_log_to_bank_transactions` للسجل العكسي
5. يقوم التريجر بإلغاء ربط المعاملة البنكية (`financial_transaction_log_id = NULL`)
6. ينشئ التريجر سجل جديد (إيداع) للقيمة الجديدة
7. يتم تشغيل تريجر `sync_log_to_bank_transactions` للسجل الجديد
8. يقوم التريجر بربط المعاملة البنكية بالسجل الجديد

### 3. حذف دفعة

```mermaid
graph TD
    A[حذف دفعة من student_payments] --> B[تريجر handle_student_payment_changes_v2]
    B --> C[إنشاء سجل عكسي للحذف]
    C --> D[تريجر sync_log_to_bank_transactions]
    D --> E[إلغاء ربط المعاملة البنكية]
    E --> F[تنظيف المعاملات غير المرتبطة]
```

**الخطوات:**
1. يتم حذف دفعة من `student_payments`
2. يتم تشغيل تريجر `handle_student_payment_changes_v2`
3. ينشئ التريجر سجل عكسي (سحب) للحذف مع `is_reversal = true`
4. يتم تشغيل تريجر `sync_log_to_bank_transactions`
5. يقوم التريجر بإلغاء ربط المعاملة البنكية
6. يتم تنظيف المعاملات البنكية غير المرتبطة

## 🛠️ التريجرات والدوال

### 1. `handle_student_payment_changes_v2()`

**الغرض:** معالجة تغييرات دفعات الطلاب وإرسالها إلى السجل المالي

**العمليات:**
- **INSERT:** إنشاء سجل مالي جديد
- **UPDATE:** إنشاء سجل عكسي + سجل جديد
- **DELETE:** إنشاء سجل عكسي للحذف

**المنطق:**
```sql
-- للإدراج: إنشاء سجل إيداع
INSERT INTO financial_transactions_log (
    transaction_type = 'إيداع',
    is_reversal = false
)

-- للتعديل: سجل عكسي + سجل جديد
INSERT INTO financial_transactions_log (
    transaction_type = 'سحب',
    is_reversal = true,
    original_log_entry_id = old_log_id
)
INSERT INTO financial_transactions_log (
    transaction_type = 'إيداع',
    is_reversal = false
)

-- للحذف: سجل عكسي
INSERT INTO financial_transactions_log (
    transaction_type = 'سحب',
    is_reversal = true,
    original_log_entry_id = old_log_id
)
```

### 2. `sync_log_to_bank_transactions()`

**الغرض:** مزامنة السجلات المالية مع المعاملات البنكية

**العمليات:**
- **INSERT (سجل عكسي):** إلغاء ربط المعاملة البنكية
- **INSERT (سجل عادي):** إنشاء أو تحديث معاملة بنكية
- **UPDATE:** تحديث المعاملة البنكية المرتبطة
- **DELETE:** حذف المعاملة البنكية

**المنطق:**
```sql
-- للسجل العكسي: إلغاء الربط
IF NEW.is_reversal = true THEN
    UPDATE bank_transactions
    SET financial_transaction_log_id = NULL
    WHERE financial_transaction_log_id = NEW.original_log_entry_id;

-- للسجل العادي: إنشاء أو تحديث
ELSIF NEW.is_reversal = false THEN
    -- البحث عن معاملة بنكية موجودة
    SELECT id FROM bank_transactions
    WHERE reference_table = NEW.source_table
    AND reference_id = NEW.source_record_id;

    IF found THEN
        -- تحديث المعاملة الموجودة
        UPDATE bank_transactions SET
            amount = NEW.amount,
            financial_transaction_log_id = NEW.id;
    ELSE
        -- إنشاء معاملة جديدة
        INSERT INTO bank_transactions (...);
    END IF;
```

## 🔍 مثال عملي

### السيناريو: دفعة طالب بقيمة 9999 ثم تعديلها إلى 8888

#### 1. الإدراج الأولي
```sql
INSERT INTO student_payments (amount = 9999, ...)
```

**النتيجة:**
- `financial_transactions_log`: سجل واحد (9999 إيداع)
- `bank_transactions`: معاملة واحدة (9999 إيداع) مرتبطة بالسجل المالي

#### 2. التعديل
```sql
UPDATE student_payments SET amount = 8888 WHERE id = ...
```

**النتيجة:**
- `financial_transactions_log`:
  - السجل الأصلي (9999 إيداع)
  - سجل عكسي (9999 سحب) يشير للأصلي
  - سجل جديد (8888 إيداع)
- `bank_transactions`:
  - نفس المعاملة محدثة (8888 إيداع) مرتبطة بالسجل الجديد

#### 3. الحذف
```sql
DELETE FROM student_payments WHERE id = ...
```

**النتيجة:**
- `financial_transactions_log`:
  - جميع السجلات السابقة
  - سجل عكسي جديد (8888 سحب) للحذف
- `bank_transactions`:
  - المعاملة غير مرتبطة (`financial_transaction_log_id = NULL`)
  - يمكن حذفها بواسطة تريجر التنظيف

## 🎯 المبادئ الأساسية

### 1. **الشفافية المالية**
- كل تغيير يتم تسجيله في السجل المالي
- السجلات العكسية تحافظ على تاريخ التغييرات
- لا يتم حذف أي سجل مالي نهائياً

### 2. **الربط الذكي**
- المعاملات البنكية مرتبطة دائماً بآخر سجل مالي صالح
- السجلات العكسية تلغي الربط تلقائياً
- معاملة بنكية واحدة لكل دفعة طالب

### 3. **التنظيف التلقائي**
- المعاملات البنكية غير المرتبطة يتم تنظيفها
- النظام يحافظ على التوازن بين الجداول

## 🚨 نقاط مهمة

### 1. **ترتيب التنفيذ**
- السجل العكسي يتم إنشاؤه أولاً (يلغي الربط)
- السجل الجديد يتم إنشاؤه ثانياً (ينشئ ربط جديد)
- هذا يضمن عدم وجود تضارب في الربط

### 2. **منع التكرار**
- التريجر يتحقق من عدم وجود دفعات مكررة لنفس الطالب في نفس الشهر
- إذا وجدت دفعة مكررة، يتم تجاهل التريجر

### 3. **الأمان**
- جميع العمليات تتم داخل معاملات قاعدة البيانات
- في حالة فشل أي خطوة، يتم التراجع عن كامل العملية

## 📊 مراقبة النظام

### استعلامات مفيدة للمراقبة:

```sql
-- التحقق من المعاملات البنكية غير المرتبطة
SELECT * FROM bank_transactions
WHERE financial_transaction_log_id IS NULL;

-- التحقق من السجلات المالية بدون معاملات بنكية
SELECT ftl.* FROM financial_transactions_log ftl
LEFT JOIN bank_transactions bt ON bt.financial_transaction_log_id = ftl.id
WHERE bt.id IS NULL AND ftl.is_reversal = false;

-- عرض تاريخ دفعة طالب معين
SELECT * FROM financial_transactions_log
WHERE source_record_id = 'payment_id'
ORDER BY created_at;

-- التحقق من توازن دفعة طالب
WITH payment_history AS (
    SELECT
        source_record_id,
        SUM(CASE WHEN is_reversal = false THEN amount ELSE -amount END) as net_amount
    FROM financial_transactions_log
    WHERE source_record_id = 'payment_id'
    GROUP BY source_record_id
)
SELECT sp.amount as original_amount, ph.net_amount, bt.amount as bank_amount
FROM student_payments sp
JOIN payment_history ph ON ph.source_record_id = sp.id::text
LEFT JOIN bank_transactions bt ON bt.reference_id = sp.id
WHERE sp.id = 'payment_id';
```

## 🔧 الصيانة

### تنظيف دوري:
```sql
-- حذف المعاملات البنكية غير المرتبطة
DELETE FROM bank_transactions
WHERE financial_transaction_log_id IS NULL;

-- التحقق من السجلات المعلقة
SELECT 'Orphaned bank transactions' as issue, COUNT(*) as count
FROM bank_transactions
WHERE financial_transaction_log_id IS NULL
UNION ALL
SELECT 'Financial logs without bank transactions' as issue, COUNT(*) as count
FROM financial_transactions_log ftl
LEFT JOIN bank_transactions bt ON bt.financial_transaction_log_id = ftl.id
WHERE bt.id IS NULL AND ftl.is_reversal = false;
```

### التحقق من التوازن:
```sql
-- التأكد من أن كل دفعة لها معاملة بنكية
SELECT sp.id, sp.amount, bt.amount as bank_amount,
       CASE WHEN bt.id IS NULL THEN 'Missing bank transaction'
            WHEN sp.amount != bt.amount THEN 'Amount mismatch'
            ELSE 'OK' END as status
FROM student_payments sp
LEFT JOIN bank_transactions bt ON bt.reference_id = sp.id
WHERE bt.id IS NULL OR sp.amount != bt.amount;
```

## 🎯 الخلاصة

هذا النظام يوفر:

1. **تتبع كامل** لجميع التغييرات المالية
2. **شفافية مطلقة** في السجلات المالية
3. **ربط ذكي** بين الجداول المختلفة
4. **تنظيف تلقائي** للبيانات المعلقة
5. **أمان عالي** ضد فقدان البيانات

النظام مصمم ليكون **قابل للتدقيق** و **موثوق** و **قابل للصيانة** مع الحفاظ على **الأداء العالي**.
